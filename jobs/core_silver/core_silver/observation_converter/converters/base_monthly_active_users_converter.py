import abc

import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.observation_converter.converters.base_active_users_converter import (
    BaseActiveUsersConverter,
)
from data_sdk.reports.schema import MonthlyActiveUsersConvertedReport


class BaseMonthlyActiveUsersConverter(BaseActiveUsersConverter, abc.ABC):
    """
    Base class for monthly active users converters.
    Handles monthly-specific logic like year/month parsing and column mapping.
    """

    converted_report_cls = MonthlyActiveUsersConvertedReport

    # Monthly-specific schema extends base schema with year/month fields
    _schema = pa.DataFrameSchema({
        **BaseActiveUsersConverter._base_schema_fields,
        "year": Column(pa.Int, coerce=True),
        "month": Column(pa.Int, coerce=True),
    })

    def _build_converted_dataframe(
        self, parsed_df: pd.DataFrame, manifest
    ) -> pd.DataFrame:
        """Build converted dataframe with monthly-specific columns."""
        common_columns = self._get_common_columns(parsed_df, manifest)

        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            **common_columns,
            # Monthly-specific columns
            year=parsed_df["year"],
            month=parsed_df["month"],
            monthly_active_users=parsed_df["count"],
        )
        return converted_df

    def _get_file_filter(self) -> callable:
        """Filter for monthly active users files."""
        return lambda filename: "monthly_" in filename

    def _get_parse_dates(self) -> bool:
        """Don't parse dates for monthly data (we have year/month)."""
        return False

    def _get_dtype_mapping(self) -> dict:
        return {
            **self._get_common_dtype_mapping(),
            "year": str,
            "month": str,
        }
