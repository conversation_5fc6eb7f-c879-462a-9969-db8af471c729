import abc

import polars as pl

from core_silver.observation_converter.deduplicators.base_deduplicator import (
    BaseDeduplicator,
)
from core_silver.utils.string_format import (
    get_date_from_from_filename,
    get_date_to_from_filename,
    get_report_id_from_filename,
)
from data_sdk.domain.domain_types import ReportMetadata, ReportState, StudioId
from data_sdk.reports.reader import ConvertedReportsReader


class LatestDeduplicator(BaseDeduplicator):
    """
    Base class for deduplicators that select the latest upload for each time period.

    Subclasses must implement:
    - _generate_time_periods: Generate time periods from date ranges
    - _get_join_keys: Return the column names to join on
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def deduplicate(
        self,
        metadata_list: list[ReportMetadata],
        converted_filename_list: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        if not metadata_list or not converted_filename_list:
            return pl.DataFrame()

        coverage = self._get_coverage(metadata_list, converted_filename_list)

        reports_data = self._load_files(
            metadata_list[0].studio_id, converted_filename_list, converted_reader
        )
        if reports_data.is_empty():
            return pl.DataFrame()

        # Join on the keys specified by subclass
        join_keys = self._get_join_keys()
        deduplicated = reports_data.join(coverage, on=join_keys, how="inner")
        return deduplicated

    def _get_coverage(
        self, metadata_list: list[ReportMetadata], converted_filepath_list: list[str]
    ) -> pl.DataFrame:
        """
        Generates coverage based on metadata and file names.
        Uses template method pattern - subclasses customize time period generation.
        """
        # Filter out failed reports
        valid_metadata = [
            obj for obj in metadata_list if obj.state != ReportState.FAILED
        ]
        if not valid_metadata:
            return pl.DataFrame()

        metadata_df = pl.DataFrame([vars(obj) for obj in valid_metadata])
        metadata_df = metadata_df[["report_id", "upload_date", "date_from", "date_to"]]

        # Create a DataFrame from the list of file names
        file_df = pl.DataFrame({"file_name": converted_filepath_list})

        # Extract report_id, date_from_from_file_name, and date_to_from_file_name
        file_df = file_df.with_columns(
            pl.col("file_name")
            .apply(get_report_id_from_filename, return_dtype=pl.Int64)
            .alias("report_id"),
            pl.col("file_name")
            .apply(get_date_from_from_filename, return_dtype=pl.Date)
            .alias("date_from_from_file_name"),
            pl.col("file_name")
            .apply(get_date_to_from_filename, return_dtype=pl.Date)
            .alias("date_to_from_file_name"),
        )
        file_df = file_df.drop("file_name")

        metadata_df = metadata_df.join(file_df, on="report_id", how="inner")

        metadata_df = metadata_df.drop(["date_from", "date_to"])
        metadata_df = metadata_df.rename({
            "date_from_from_file_name": "date_from",
            "date_to_from_file_name": "date_to",
        })

        # Generate time periods - delegated to subclass
        metadata_df = self._generate_time_periods(metadata_df)

        # Sort by upload_date (latest first), then by time period columns
        sort_columns = ["upload_date"] + self._get_time_period_columns()
        sort_descending = [True] + [False] * len(self._get_time_period_columns())
        metadata_df = metadata_df.sort(by=sort_columns, descending=sort_descending)

        metadata_df = metadata_df.drop(["date_from", "date_to", "upload_date"])

        # Keep only the latest report for each time period
        unique_columns = self._get_time_period_columns()
        metadata_df = metadata_df.unique(subset=unique_columns, keep="first")
        return metadata_df

    def _load_files(
        self,
        studio_id: StudioId,
        filenames: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        all_files = [
            converted_reader.read(f"studio_id={studio_id}/{filename}")
            for filename in filenames
        ]
        # Polars saves Enum column in empty df as Categorical
        # and we can't concat dfs with different columns types
        all_non_empty_files = [file for file in all_files if not file.is_empty()]
        if len(all_non_empty_files) == 0:
            return pl.DataFrame()
        return pl.concat([file for file in all_files if not file.is_empty()])

    @abc.abstractmethod
    def _generate_time_periods(self, metadata_df: pl.DataFrame) -> pl.DataFrame:
        """
        Generate time periods from date ranges.

        Args:
            metadata_df: DataFrame with columns [report_id, upload_date, date_from, date_to]

        Returns:
            DataFrame with time period columns added and exploded
        """
        pass

    @abc.abstractmethod
    def _get_join_keys(self) -> list[str]:
        """
        Return the column names to join on.

        Returns:
            List of column names for joining reports_data with coverage
        """
        pass

    @abc.abstractmethod
    def _get_time_period_columns(self) -> list[str]:
        """
        Return the column names that represent time periods.

        Returns:
            List of column names used for sorting and uniqueness
        """
        pass
