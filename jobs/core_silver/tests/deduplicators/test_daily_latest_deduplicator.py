from datetime import date, datetime
from unittest.mock import MagicMock

import polars as pl
import pytest
from zoneinfo import ZoneInfo

from core_silver.observation_converter.deduplicators.daily_latest_deduplicator import (
    DailyLatestDeduplicator,
)
from core_silver.utils.string_format import generate_expected_filename
from data_sdk.domain.domain_types import ReportMetadata, ReportState
from data_sdk.domain.source import Source
from data_sdk.reports.reader import ConvertedReportsReader


@pytest.fixture
def mock_converted_reader():
    return MagicMock(spec=ConvertedReportsReader)


def create_sales_report_metadata(
    report_id: int,
    upload_date: datetime,
    date_from: date,
    date_to: date,
    state: ReportState = ReportState.PENDING,
) -> ReportMetadata:
    return ReportMetadata(
        source=Source.STEAM_SALES,
        date_from=date_from,
        date_to=date_to,
        studio_id=1,
        report_id=report_id,  # type: ignore[arg-type]
        upload_date=upload_date,
        blob_name=f"sales_report_{report_id}.zip",  # type: ignore[arg-type]
        original_name=f"sales_report_{report_id}.zip",
        state=state,
        no_data=False,
    )


def create_sales_dataframe(
    date_val: date, sales_amount: int, report_id: int
) -> pl.DataFrame:
    return pl.DataFrame({
        "date": [date_val],
        "sku_id": ["SKU123"],
        "gross_sales": [sales_amount],
        "report_id": [report_id],
        "studio_id": [1],
        "platform": ["PC"],
        "region": ["Global"],
        "portal": ["Steam"],
        "store": ["Steam"],
        "abbreviated_name": ["Steam"],
        "country_code": ["ZZZ"],
        "human_name": ["Test Game"],
        "store_id": ["SKU123"],
        "unique_sku_id": ["SKU123-steam:1"],
        "portal_platform_region": ["Steam:PC:Global"],
        "net_sales": [sales_amount * 0.7],
        "units_sold": [1],
    })


def test_deduplicate_single_day_single_report(mock_converted_reader):
    """Test basic case: single report with single day"""
    report = create_sales_report_metadata(
        report_id=1,
        upload_date=datetime(2024, 7, 1, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 1),
    )

    sales_data = create_sales_dataframe(date(2024, 6, 1), 1000, 1)
    mock_converted_reader.read.return_value = sales_data

    deduplicator = DailyLatestDeduplicator()
    result = deduplicator.deduplicate(
        [report], [generate_expected_filename(report)], mock_converted_reader
    )

    assert not result.is_empty()
    assert result.height == 1
    assert result["date"][0] == date(2024, 6, 1)
    assert result["gross_sales"][0] == 1000


def test_deduplicate_same_day_multiple_reports_takes_latest(mock_converted_reader):
    """Test conflict resolution: same day in multiple reports, should take latest upload"""
    old_report = create_sales_report_metadata(
        report_id=1,
        upload_date=datetime(2024, 6, 1, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 1),
    )

    new_report = create_sales_report_metadata(
        report_id=2,
        upload_date=datetime(2024, 7, 1, tzinfo=ZoneInfo("UTC")),  # Later upload
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 1),
    )

    old_data = create_sales_dataframe(date(2024, 6, 1), 1000, 1)  # Old data
    new_data = create_sales_dataframe(date(2024, 6, 1), 1500, 2)  # New data

    def mock_read(path):
        if "1_2024-06-01_2024-06-01.parquet" in path:
            return old_data
        elif "2_2024-06-01_2024-06-01.parquet" in path:
            return new_data
        return pl.DataFrame()

    mock_converted_reader.read.side_effect = mock_read

    deduplicator = DailyLatestDeduplicator()
    result = deduplicator.deduplicate(
        [old_report, new_report],
        [
            generate_expected_filename(old_report),
            generate_expected_filename(new_report),
        ],
        mock_converted_reader,
    )

    assert not result.is_empty()
    assert result.height == 1
    assert result["gross_sales"][0] == 1500  # Should take newer data
    assert result["report_id"][0] == 2  # Should be from newer report


def test_deduplicate_different_days_keeps_all(mock_converted_reader):
    """Test non-overlapping days: should keep data from all days"""
    day1_report = create_sales_report_metadata(
        report_id=1,
        upload_date=datetime(2024, 7, 1, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 1),
    )

    day2_report = create_sales_report_metadata(
        report_id=2,
        upload_date=datetime(2024, 7, 2, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 2),
        date_to=date(2024, 6, 2),
    )

    day1_data = create_sales_dataframe(date(2024, 6, 1), 1000, 1)
    day2_data = create_sales_dataframe(date(2024, 6, 2), 1200, 2)

    def mock_read(path):
        if "1_2024-06-01_2024-06-01.parquet" in path:
            return day1_data
        elif "2_2024-06-02_2024-06-02.parquet" in path:
            return day2_data
        return pl.DataFrame()

    mock_converted_reader.read.side_effect = mock_read

    deduplicator = DailyLatestDeduplicator()
    result = deduplicator.deduplicate(
        [day1_report, day2_report],
        [
            generate_expected_filename(day1_report),
            generate_expected_filename(day2_report),
        ],
        mock_converted_reader,
    )

    assert not result.is_empty()
    assert result.height == 2

    # Sort by date to ensure consistent ordering
    result = result.sort("date")
    assert result["date"][0] == date(2024, 6, 1)
    assert result["gross_sales"][0] == 1000
    assert result["date"][1] == date(2024, 6, 2)
    assert result["gross_sales"][1] == 1200


def test_deduplicate_overlapping_periods_takes_latest_per_day(mock_converted_reader):
    """Test overlapping periods: should take latest upload for each day"""
    # Report 1: covers June 1-3, uploaded June 5
    report1 = create_sales_report_metadata(
        report_id=1,
        upload_date=datetime(2024, 6, 5, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 3),
    )

    # Report 2: covers June 2-4, uploaded June 6 (later)
    report2 = create_sales_report_metadata(
        report_id=2,
        upload_date=datetime(2024, 6, 6, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 2),
        date_to=date(2024, 6, 4),
    )

    # Report 1 data: June 1-3
    report1_data = pl.concat([
        create_sales_dataframe(date(2024, 6, 1), 100, 1),
        create_sales_dataframe(date(2024, 6, 2), 200, 1),
        create_sales_dataframe(date(2024, 6, 3), 300, 1),
    ])

    # Report 2 data: June 2-4 (newer data for June 2-3, new data for June 4)
    report2_data = pl.concat([
        create_sales_dataframe(date(2024, 6, 2), 250, 2),  # Newer than report1
        create_sales_dataframe(date(2024, 6, 3), 350, 2),  # Newer than report1
        create_sales_dataframe(date(2024, 6, 4), 400, 2),  # Only in report2
    ])

    def mock_read(path):
        if "1_2024-06-01_2024-06-03.parquet" in path:
            return report1_data
        elif "2_2024-06-02_2024-06-04.parquet" in path:
            return report2_data
        return pl.DataFrame()

    mock_converted_reader.read.side_effect = mock_read

    deduplicator = DailyLatestDeduplicator()
    result = deduplicator.deduplicate(
        [report1, report2],
        [generate_expected_filename(report1), generate_expected_filename(report2)],
        mock_converted_reader,
    )

    assert not result.is_empty()
    assert result.height == 4

    # Sort by date to ensure consistent ordering
    result = result.sort("date")

    # June 1: only in report1
    assert result["date"][0] == date(2024, 6, 1)
    assert result["gross_sales"][0] == 100
    assert result["report_id"][0] == 1

    # June 2: in both reports, should take report2 (later upload)
    assert result["date"][1] == date(2024, 6, 2)
    assert result["gross_sales"][1] == 250
    assert result["report_id"][1] == 2

    # June 3: in both reports, should take report2 (later upload)
    assert result["date"][2] == date(2024, 6, 3)
    assert result["gross_sales"][2] == 350
    assert result["report_id"][2] == 2

    # June 4: only in report2
    assert result["date"][3] == date(2024, 6, 4)
    assert result["gross_sales"][3] == 400
    assert result["report_id"][3] == 2


# TODO: remove
def test_deduplicate_ignores_failed_reports(mock_converted_reader):
    """Test that failed reports are ignored in deduplication"""
    good_report = create_sales_report_metadata(
        report_id=1,
        upload_date=datetime(2024, 6, 1, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 1),
        state=ReportState.PENDING,
    )

    failed_report = create_sales_report_metadata(
        report_id=2,
        upload_date=datetime(2024, 7, 1, tzinfo=ZoneInfo("UTC")),  # Later upload
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 1),
        state=ReportState.FAILED,
    )

    good_data = create_sales_dataframe(date(2024, 6, 1), 1000, 1)

    def mock_read(path):
        if "1_2024-06-01_2024-06-01.parquet" in path:
            return good_data
        return pl.DataFrame()

    mock_converted_reader.read.side_effect = mock_read

    deduplicator = DailyLatestDeduplicator()
    result = deduplicator.deduplicate(
        [good_report, failed_report],
        [
            generate_expected_filename(good_report),
            generate_expected_filename(failed_report),
        ],
        mock_converted_reader,
    )

    assert not result.is_empty()
    assert result.height == 1
    assert result["gross_sales"][0] == 1000  # Should take good report data
    assert result["report_id"][0] == 1  # Should be from good report


def test_deduplicate_empty_inputs_returns_empty(mock_converted_reader):
    """Test edge case: empty inputs should return empty DataFrame"""
    deduplicator = DailyLatestDeduplicator()

    # Test empty metadata list
    result = deduplicator.deduplicate([], ["some_file.parquet"], mock_converted_reader)
    assert result.is_empty()

    # Test empty filename list
    now_date = datetime.now(ZoneInfo("UTC")).date()
    result = deduplicator.deduplicate(
        [
            create_sales_report_metadata(
                1,
                datetime.now(ZoneInfo("UTC")),
                now_date,
                now_date,
            )
        ],
        [],
        mock_converted_reader,
    )
    assert result.is_empty()
