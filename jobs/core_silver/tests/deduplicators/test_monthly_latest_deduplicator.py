from datetime import date, datetime
from unittest.mock import MagicMock

import polars as pl
import pytest
from zoneinfo import ZoneInfo

from core_silver.observation_converter.deduplicators.monthly_latest_deduplicator import (
    MonthlyLatestDeduplicator,
)
from core_silver.utils.string_format import generate_expected_filename
from data_sdk.domain.domain_types import ReportMetadata, ReportState
from data_sdk.domain.source import Source
from data_sdk.reports.reader import ConvertedReportsReader


@pytest.fixture
def mock_converted_reader():
    return MagicMock(spec=ConvertedReportsReader)


def create_mau_report_metadata(
    report_id: int,
    upload_date: datetime,
    date_from: date,
    date_to: date,
    state: ReportState = ReportState.PENDING,  # TODO: stringiem
) -> ReportMetadata:
    return ReportMetadata(
        source=Source.MICROSOFT_MONTHLY_ACTIVE_USERS,
        date_from=date_from,
        date_to=date_to,
        studio_id=1,
        report_id=report_id,  # type: ignore[arg-type]
        upload_date=upload_date,
        blob_name=f"mau_report_{report_id}.zip",  # type: ignore[arg-type]
        original_name=f"mau_report_{report_id}.zip",
        state=state,
        no_data=False,
    )


def create_mau_dataframe(
    year: int, month: int, sku_id: str, mau_count: int, report_id: int
) -> pl.DataFrame:
    return pl.DataFrame({
        "year": [year],
        "month": [month],
        "sku_id": [sku_id],
        "monthly_active_users": [mau_count],
        "report_id": [report_id],
        "studio_id": [1],
        "platform": ["PC"],
        "region": ["Global"],
        "portal": ["Microsoft"],
        "store": ["Microsoft"],
        "abbreviated_name": ["Microsoft"],
        "country_code": ["ZZZ"],
        "human_name": ["Test Game"],
        "store_id": [sku_id],
        "unique_sku_id": [f"{sku_id}-microsoft:1"],
        "portal_platform_region": ["Microsoft:PC:Global"],
    })


def test_deduplicate_single_month_single_report(mock_converted_reader):
    """Test basic case: single report with single month"""
    report = create_mau_report_metadata(
        report_id=1,
        upload_date=datetime(2024, 7, 1, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 30),
    )

    mau_data = create_mau_dataframe(2024, 6, "SKU123", 1500, 1)
    mock_converted_reader.read.return_value = mau_data

    deduplicator = MonthlyLatestDeduplicator()
    result = deduplicator.deduplicate(
        [report], [generate_expected_filename(report)], mock_converted_reader
    )

    assert not result.is_empty()
    assert result.height == 1
    assert result["year"][0] == 2024
    assert result["month"][0] == 6
    assert result["monthly_active_users"][0] == 1500


def test_deduplicate_same_month_multiple_reports_takes_latest(mock_converted_reader):
    """Test conflict resolution: same month in multiple reports, should take latest upload"""
    old_report = create_mau_report_metadata(
        report_id=1,
        upload_date=datetime(2024, 6, 1, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 30),
    )

    new_report = create_mau_report_metadata(
        report_id=2,
        upload_date=datetime(2024, 7, 1, tzinfo=ZoneInfo("UTC")),  # Later upload
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 30),
    )

    old_data = create_mau_dataframe(2024, 6, "SKU123", 1000, 1)  # Old data
    new_data = create_mau_dataframe(2024, 6, "SKU123", 1500, 2)  # New data

    def mock_read(path):
        if "1_2024-06-01_2024-06-30.parquet" in path:
            return old_data
        elif "2_2024-06-01_2024-06-30.parquet" in path:
            return new_data
        return pl.DataFrame()

    mock_converted_reader.read.side_effect = mock_read

    deduplicator = MonthlyLatestDeduplicator()
    result = deduplicator.deduplicate(
        [old_report, new_report],
        [
            generate_expected_filename(old_report),
            generate_expected_filename(new_report),
        ],
        mock_converted_reader,
    )

    assert not result.is_empty()
    assert result.height == 1
    assert result["monthly_active_users"][0] == 1500  # Should take newer data
    assert result["report_id"][0] == 2  # Should be from newer report


def test_deduplicate_different_months_keeps_all(mock_converted_reader):
    """Test non-overlapping months: should keep data from all months"""
    june_report = create_mau_report_metadata(
        report_id=1,
        upload_date=datetime(2024, 7, 1, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 30),
    )

    july_report = create_mau_report_metadata(
        report_id=2,
        upload_date=datetime(2024, 8, 1, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 7, 1),
        date_to=date(2024, 7, 31),
    )

    june_data = create_mau_dataframe(2024, 6, "SKU123", 1000, 1)
    july_data = create_mau_dataframe(2024, 7, "SKU123", 1200, 2)

    def mock_read(path):
        if "1_2024-06-01_2024-06-30.parquet" in path:
            return june_data
        elif "2_2024-07-01_2024-07-31.parquet" in path:
            return july_data
        return pl.DataFrame()

    mock_converted_reader.read.side_effect = mock_read

    deduplicator = MonthlyLatestDeduplicator()
    result = deduplicator.deduplicate(
        [june_report, july_report],
        [
            generate_expected_filename(june_report),
            generate_expected_filename(july_report),
        ],
        mock_converted_reader,
    )

    assert not result.is_empty()
    assert result.height == 2

    # Sort by month to ensure consistent ordering
    result = result.sort("month")
    assert result["month"][0] == 6
    assert result["monthly_active_users"][0] == 1000
    assert result["month"][1] == 7
    assert result["monthly_active_users"][1] == 1200


def test_deduplicate_ignores_failed_reports(mock_converted_reader):
    """Test that failed reports are ignored in deduplication"""
    good_report = create_mau_report_metadata(
        report_id=1,
        upload_date=datetime(2024, 6, 1, tzinfo=ZoneInfo("UTC")),
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 30),
        state=ReportState.PENDING,
    )

    failed_report = create_mau_report_metadata(
        report_id=2,
        upload_date=datetime(2024, 7, 1, tzinfo=ZoneInfo("UTC")),  # Later upload
        date_from=date(2024, 6, 1),
        date_to=date(2024, 6, 30),
        state=ReportState.FAILED,
    )

    good_data = create_mau_dataframe(2024, 6, "SKU123", 1000, 1)

    def mock_read(path):
        if "1_2024-06-01_2024-06-30.parquet" in path:
            return good_data
        return pl.DataFrame()

    mock_converted_reader.read.side_effect = mock_read

    deduplicator = MonthlyLatestDeduplicator()
    result = deduplicator.deduplicate(
        [good_report, failed_report],
        [
            generate_expected_filename(good_report),
            generate_expected_filename(failed_report),
        ],
        mock_converted_reader,
    )

    assert not result.is_empty()
    assert result.height == 1
    assert result["monthly_active_users"][0] == 1000  # Should take good report data
    assert result["report_id"][0] == 1  # Should be from good report


def test_deduplicate_empty_inputs_returns_empty(mock_converted_reader):
    """Test edge case: empty inputs should return empty DataFrame"""
    deduplicator = MonthlyLatestDeduplicator()

    # Test empty metadata list
    result = deduplicator.deduplicate([], ["some_file.parquet"], mock_converted_reader)
    assert result.is_empty()

    # Test empty filename list
    now_date = datetime.now(ZoneInfo("UTC")).date()
    result = deduplicator.deduplicate(
        [
            create_mau_report_metadata(
                1,
                datetime.now(ZoneInfo("UTC")),
                now_date,
                now_date,
            )
        ],
        [],
        mock_converted_reader,
    )
    assert result.is_empty()
